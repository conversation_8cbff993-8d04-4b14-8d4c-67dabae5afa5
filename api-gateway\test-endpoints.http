### ATMA API Gateway - Test Endpoints
### Base URL: http://localhost:3000

### 1. Gateway Health Check
GET http://localhost:3000/health

### 2. Detailed Health Check
GET http://localhost:3000/health/detailed

### 3. Gateway Info
GET http://localhost:3000/

### ===== AUTH SERVICE TESTS =====

### 4. User Registration
POST http://localhost:3000/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 5. User Login
POST http://localhost:3000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 6. Get User Profile (Replace TOKEN with actual JWT)
GET http://localhost:3000/api/auth/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 7. Update User Profile
PUT http://localhost:3000/api/auth/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "username": "testuser",
  "full_name": "Test User",
  "school_id": 1,
  "date_of_birth": "1995-01-01",
  "gender": "male"
}

### 8. Get Schools List
GET http://localhost:3000/api/auth/schools?page=1&limit=10
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 9. Get Token Balance
GET http://localhost:3000/api/auth/token-balance
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### ===== ADMIN TESTS =====

### 10. Admin Login
POST http://localhost:3000/api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 11. Get Admin Profile
GET http://localhost:3000/api/admin/profile
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN_HERE

### ===== ASSESSMENT SERVICE TESTS =====

### 12. Submit Assessment
POST http://localhost:3000/api/assessment/submit
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30,
    "openness": 80
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60
  }
}

### 13. Check Assessment Status (Replace JOB_ID with actual job ID)
GET http://localhost:3000/api/assessment/status/YOUR_JOB_ID_HERE
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### ===== ARCHIVE SERVICE TESTS =====

### 14. Get Analysis Results
GET http://localhost:3000/api/archive/results?page=1&limit=10
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 15. Get Specific Analysis Result (Replace RESULT_ID with actual ID)
GET http://localhost:3000/api/archive/results/YOUR_RESULT_ID_HERE
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 16. Get Analysis Jobs
GET http://localhost:3000/api/archive/jobs?page=1&limit=10
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 17. Get Job Statistics
GET http://localhost:3000/api/archive/jobs/stats
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### ===== INTERNAL SERVICE TESTS =====

### 18. Verify Token (Internal Service)
POST http://localhost:3000/api/auth/verify-token
X-Service-Key: internal_service_secret_key_change_in_production
X-Internal-Service: true
Content-Type: application/json

{
  "token": "YOUR_JWT_TOKEN_HERE"
}

### ===== ERROR TESTS =====

### 19. Test 404 - Unknown Route
GET http://localhost:3000/api/unknown-endpoint

### 20. Test Rate Limiting (Run multiple times quickly)
GET http://localhost:3000/api/auth/profile

### 21. Test Unauthorized Access
GET http://localhost:3000/api/auth/profile

### 22. Test Invalid Token
GET http://localhost:3000/api/auth/profile
Authorization: Bearer invalid_token_here

### ===== SERVICE HEALTH TESTS =====

### 23. Auth Service Health
GET http://localhost:3000/api/auth/health

### 24. Archive Service Health  
GET http://localhost:3000/api/archive/health

### 25. Assessment Service Health
GET http://localhost:3000/api/assessment/health
