@echo off
echo ========================================
echo Starting ATMA Backend Services with API Gateway
echo ========================================

echo.
echo Installing API Gateway dependencies...
cd api-gateway
if not exist node_modules (
    echo Installing npm packages for API Gateway...
    call npm install
    if errorlevel 1 (
        echo Failed to install API Gateway dependencies
        pause
        exit /b 1
    )
) else (
    echo API Gateway dependencies already installed.
)
cd ..

echo.
echo Starting all services...
echo.

REM Start Auth Service
echo Starting Auth Service on port 3001...
start "Auth Service" cmd /k "cd auth-service && npm start"
timeout /t 3 /nobreak >nul

REM Start Archive Service  
echo Starting Archive Service on port 3002...
start "Archive Service" cmd /k "cd archive-service && npm start"
timeout /t 3 /nobreak >nul

REM Start Assessment Service
echo Starting Assessment Service on port 3003...
start "Assessment Service" cmd /k "cd assessment-service && npm start"
timeout /t 3 /nobreak >nul

REM Wait for services to start
echo.
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Start API Gateway
echo Starting API Gateway on port 3000...
start "API Gateway" cmd /k "cd api-gateway && npm start"
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo All services started successfully!
echo ========================================
echo.
echo Services running:
echo - API Gateway:        http://localhost:3000
echo - Auth Service:       http://localhost:3001  
echo - Archive Service:    http://localhost:3002
echo - Assessment Service: http://localhost:3003
echo.
echo API Gateway endpoints:
echo - Health Check:       http://localhost:3000/health
echo - Detailed Health:    http://localhost:3000/health/detailed
echo - API Documentation:  http://localhost:3000/
echo.
echo Use the API Gateway (port 3000) as your main endpoint!
echo All API calls should go through: http://localhost:3000/api/
echo.
echo Press any key to exit...
pause >nul
